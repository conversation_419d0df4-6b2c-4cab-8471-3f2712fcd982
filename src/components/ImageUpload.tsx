'use client';

import React, { useState, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Camera, Upload, X, Check, AlertCircle, Loader2 } from 'lucide-react';
import { compressImageFile, validateImageFile, formatFileSize, createImagePreview, cleanupImagePreview } from '@/lib/client-image-compression';

interface ImageUploadProps {
  currentImage?: string;
  onImageUpload: (imageUrl: string) => void;
  onImageRemove?: () => void;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
}

interface UploadState {
  uploading: boolean;
  compressing: boolean;
  progress: number;
  error: string | null;
  success: boolean;
}

export default function ImageUpload({
  currentImage,
  onImageUpload,
  onImageRemove,
  className = '',
  size = 'md',
  disabled = false,
}: ImageUploadProps) {
  const [uploadState, setUploadState] = useState<UploadState>({
    uploading: false,
    compressing: false,
    progress: 0,
    error: null,
    success: false,
  });
  const [preview, setPreview] = useState<string | null>(null);
  const [compressionInfo, setCompressionInfo] = useState<{
    originalSize: number;
    compressedSize: number;
    compressionRatio: number;
  } | null>(null);
  
  const fileInputRef = useRef<HTMLInputElement>(null);

  const sizeClasses = {
    sm: 'w-16 h-16',
    md: 'w-32 h-32',
    lg: 'w-48 h-48',
  };

  const iconSizes = {
    sm: 16,
    md: 24,
    lg: 32,
  };

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Reset state
    setUploadState({
      uploading: false,
      compressing: false,
      progress: 0,
      error: null,
      success: false,
    });
    setCompressionInfo(null);

    // Validate file
    const validation = validateImageFile(file);
    if (!validation.valid) {
      setUploadState(prev => ({ ...prev, error: validation.error || 'Invalid file' }));
      return;
    }

    try {
      // Show compression progress
      setUploadState(prev => ({ ...prev, compressing: true }));

      // Compress image
      const compressionResult = await compressImageFile(file, {
        maxSizeKB: 100,
        maxWidth: 800,
        maxHeight: 800,
        quality: 85,
        format: 'jpeg',
      });

      setCompressionInfo({
        originalSize: compressionResult.originalSize,
        compressedSize: compressionResult.compressedSize,
        compressionRatio: compressionResult.compressionRatio,
      });

      // Create preview
      const previewUrl = createImagePreview(compressionResult.file);
      setPreview(previewUrl);

      setUploadState(prev => ({ ...prev, compressing: false, uploading: true }));

      // Upload to server
      const formData = new FormData();
      formData.append('image', compressionResult.file);

      const response = await fetch('/api/upload/profile-image', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Upload failed');
      }

      const result = await response.json();
      
      setUploadState(prev => ({ ...prev, uploading: false, success: true }));
      onImageUpload(result.imageUrl);

      // Clean up preview after success
      setTimeout(() => {
        if (previewUrl) {
          cleanupImagePreview(previewUrl);
          setPreview(null);
        }
        setUploadState(prev => ({ ...prev, success: false }));
        setCompressionInfo(null);
      }, 2000);

    } catch (error) {
      console.error('Upload error:', error);
      setUploadState(prev => ({
        ...prev,
        uploading: false,
        compressing: false,
        error: error instanceof Error ? error.message : 'Upload failed',
      }));
      
      // Clean up preview on error
      if (preview) {
        cleanupImagePreview(preview);
        setPreview(null);
      }
    }

    // Reset file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleRemoveImage = async () => {
    if (!onImageRemove) return;

    try {
      const response = await fetch('/api/upload/profile-image', {
        method: 'DELETE',
      });

      if (response.ok) {
        onImageRemove();
        setUploadState(prev => ({ ...prev, success: true }));
        setTimeout(() => {
          setUploadState(prev => ({ ...prev, success: false }));
        }, 2000);
      }
    } catch (error) {
      console.error('Remove error:', error);
      setUploadState(prev => ({ ...prev, error: 'Failed to remove image' }));
    }
  };

  const triggerFileSelect = () => {
    if (disabled || uploadState.uploading || uploadState.compressing) return;
    fileInputRef.current?.click();
  };

  return (
    <div className={`relative ${className}`}>
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileSelect}
        className="hidden"
        disabled={disabled}
      />

      {/* Main Image Display */}
      <div className={`relative ${sizeClasses[size]} rounded-full overflow-hidden border-4 border-white/20 shadow-2xl`}>
        {(preview || currentImage) ? (
          <motion.img
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            src={preview || currentImage}
            alt="Profile"
            className="w-full h-full object-cover"
          />
        ) : (
          <div className="w-full h-full bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
            <Camera size={iconSizes[size]} className="text-gray-400" />
          </div>
        )}

        {/* Upload Overlay */}
        <AnimatePresence>
          {(uploadState.uploading || uploadState.compressing) && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="absolute inset-0 bg-black/50 flex items-center justify-center"
            >
              <div className="text-center text-white">
                <Loader2 size={iconSizes[size]} className="animate-spin mx-auto mb-2" />
                <div className="text-xs">
                  {uploadState.compressing ? 'Compressing...' : 'Uploading...'}
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Success Overlay */}
        <AnimatePresence>
          {uploadState.success && (
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              className="absolute inset-0 bg-green-500/90 flex items-center justify-center"
            >
              <Check size={iconSizes[size]} className="text-white" />
            </motion.div>
          )}
        </AnimatePresence>

        {/* Upload Button */}
        <motion.button
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
          onClick={triggerFileSelect}
          disabled={disabled || uploadState.uploading || uploadState.compressing}
          className="absolute -bottom-2 -right-2 bg-blue-600 hover:bg-blue-700 text-white rounded-full p-2 shadow-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <Upload size={16} />
        </motion.button>

        {/* Remove Button */}
        {currentImage && onImageRemove && !preview && (
          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            onClick={handleRemoveImage}
            className="absolute -top-2 -right-2 bg-red-600 hover:bg-red-700 text-white rounded-full p-1 shadow-lg transition-colors"
          >
            <X size={12} />
          </motion.button>
        )}
      </div>

      {/* Compression Info */}
      <AnimatePresence>
        {compressionInfo && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="mt-2 text-xs text-gray-600 bg-gray-50 rounded-lg p-2"
          >
            <div className="flex justify-between">
              <span>Original:</span>
              <span>{formatFileSize(compressionInfo.originalSize)}</span>
            </div>
            <div className="flex justify-between">
              <span>Compressed:</span>
              <span>{formatFileSize(compressionInfo.compressedSize)}</span>
            </div>
            <div className="flex justify-between font-medium text-green-600">
              <span>Saved:</span>
              <span>{compressionInfo.compressionRatio}%</span>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Error Message */}
      <AnimatePresence>
        {uploadState.error && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="mt-2 text-xs text-red-600 bg-red-50 rounded-lg p-2 flex items-center space-x-1"
          >
            <AlertCircle size={12} />
            <span>{uploadState.error}</span>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}

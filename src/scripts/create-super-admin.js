/**
 * <PERSON><PERSON><PERSON> to create a super admin user
 * Run with: node src/scripts/create-super-admin.js
 */

const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

// Define schemas inline since we can't import TypeScript models
const OrganizationSchema = new mongoose.Schema({
  name: { type: String, required: true },
  domain: { type: String, required: true, unique: true },
  subdomain: { type: String, required: true, unique: true },
  description: String,
  industry: String,
  size: { type: String, enum: ['startup', 'small', 'medium', 'large', 'enterprise'], default: 'small' },
  website: String,
  settings: {
    allowPublicProfiles: { type: Boolean, default: true },
    requireEmailVerification: { type: Boolean, default: false },
    enableAchievements: { type: Boolean, default: true },
    enableThanks: { type: Boolean, default: true },
    customBranding: { type: Boolean, default: false },
    maxEmployees: { type: Number, default: 50 },
  },
  subscription: {
    plan: { type: String, enum: ['free', 'basic', 'premium', 'enterprise'], default: 'free' },
    status: { type: String, enum: ['active', 'inactive', 'suspended', 'cancelled'], default: 'active' },
    startDate: { type: Date, default: Date.now },
    endDate: Date,
    features: [String],
  },
  isActive: { type: Boolean, default: true },
}, { timestamps: true });

const EmployeeSchema = new mongoose.Schema({
  organization: { type: mongoose.Schema.Types.ObjectId, ref: 'Organization', required: true },
  name: { type: String, required: true },
  email: { type: String, required: true },
  password: { type: String, select: false },
  role: { type: String, enum: ['employee', 'manager', 'hr', 'admin', 'super_admin'], default: 'employee' },
  department: String,
  position: String,
  bio: String,
  isActive: { type: Boolean, default: true },
}, { timestamps: true });

const Organization = mongoose.model('Organization', OrganizationSchema);
const Employee = mongoose.model('Employee', EmployeeSchema);

async function createSuperAdmin() {
  try {
    // Connect to MongoDB
    const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/employee-bio-system';
    
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to MongoDB');

    // Check if demo organization exists, create if not
    let demoOrg = await Organization.findOne({ subdomain: 'demo' });
    if (!demoOrg) {
      demoOrg = new Organization({
        name: 'Demo Company',
        domain: 'demo.localhost',
        subdomain: 'demo',
        description: 'Demo organization for development and testing',
        industry: 'Technology',
        size: 'small',
        settings: {
          allowPublicProfiles: true,
          requireEmailVerification: false,
          enableAchievements: true,
          enableThanks: true,
          customBranding: false,
          maxEmployees: 100,
        },
        subscription: {
          plan: 'premium',
          status: 'active',
          startDate: new Date(),
          features: ['thanks', 'achievements', 'analytics', 'custom_branding'],
        },
        isActive: true,
      });
      await demoOrg.save();
      console.log('✅ Demo organization created');
    }

    // Check if super admin already exists
    const existingSuperAdmin = await Employee.findOne({ 
      role: 'super_admin',
      email: '<EMAIL>'
    });

    if (existingSuperAdmin) {
      console.log('❌ Super admin already exists');
      return;
    }

    // Create super admin user
    const hashedPassword = await bcrypt.hash('superadmin123', 12);
    
    const superAdmin = new Employee({
      organization: demoOrg._id,
      name: 'Super Administrator',
      email: '<EMAIL>',
      password: hashedPassword,
      role: 'super_admin',
      department: 'IT',
      position: 'System Administrator',
      bio: 'System super administrator with full access to all organizations',
      isActive: true,
    });

    await superAdmin.save();
    console.log('✅ Super admin created successfully');
    console.log('📧 Email: <EMAIL>');
    console.log('🔑 Password: superadmin123');
    console.log('🏢 Organization: Demo Company');

    // Also create a regular admin for testing
    const existingAdmin = await Employee.findOne({ 
      role: 'admin',
      email: '<EMAIL>'
    });

    if (!existingAdmin) {
      const adminPassword = await bcrypt.hash('admin123', 12);
      
      const admin = new Employee({
        organization: demoOrg._id,
        name: 'Regular Administrator',
        email: '<EMAIL>',
        password: adminPassword,
        role: 'admin',
        department: 'IT',
        position: 'Administrator',
        bio: 'Organization administrator with access to Demo Company only',
        isActive: true,
      });

      await admin.save();
      console.log('✅ Regular admin created successfully');
      console.log('📧 Email: <EMAIL>');
      console.log('🔑 Password: admin123');
    }

  } catch (error) {
    console.error('❌ Error creating super admin:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
}

// Run the script
createSuperAdmin();

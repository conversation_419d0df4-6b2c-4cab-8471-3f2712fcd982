import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/mongodb';
import Employee from '@/models/Employee';
import { withAuth, getAuthenticatedUser } from '@/lib/middleware/auth';
import { isValidRole } from '@/lib/roles';
import { getOrganizationFromRequest } from '@/lib/middleware/organization';

/**
 * GET /api/admin/users/[id]
 * Get a specific user by ID (admin only)
 */
export const GET = withAuth(async (
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) => {
  try {
    await dbConnect();
    const { id } = await params;

    const user = await Employee.findById(id).select('-password');
    
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      user,
      message: 'User retrieved successfully'
    });

  } catch (error) {
    console.error('Error fetching user:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}, { requireAdminOrSuperAdmin: true });

/**
 * PUT /api/admin/users/[id]
 * Update a user (admin or super admin only)
 */
export const PUT = withAuth(async (
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) => {
  try {
    await dbConnect();
    const { id } = await params;
    const body = await request.json();
    
    const { name, email, role, department, position, location, bio, skills, linkedIn, github } = body;

    // Validate role if provided
    if (role && !isValidRole(role)) {
      return NextResponse.json(
        { error: 'Invalid role specified' },
        { status: 400 }
      );
    }

    // Check if email is already taken by another user
    if (email) {
      const existingUser = await Employee.findOne({ 
        email, 
        _id: { $ne: id } 
      });
      
      if (existingUser) {
        return NextResponse.json(
          { error: 'Email is already taken by another user' },
          { status: 409 }
        );
      }
    }

    // Update user
    const updatedUser = await Employee.findByIdAndUpdate(
      id,
      {
        ...(name && { name }),
        ...(email && { email }),
        ...(role && { role }),
        ...(department !== undefined && { department }),
        ...(position !== undefined && { position }),
        ...(location !== undefined && { location }),
        ...(bio !== undefined && { bio }),
        ...(skills !== undefined && { skills }),
        ...(linkedIn !== undefined && { linkedIn }),
        ...(github !== undefined && { github }),
        updatedAt: new Date(),
      },
      { 
        new: true, 
        runValidators: true 
      }
    ).select('-password');

    if (!updatedUser) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      user: updatedUser,
      message: 'User updated successfully'
    });

  } catch (error) {
    console.error('Error updating user:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}, { requireAdminOrSuperAdmin: true });

/**
 * DELETE /api/admin/users/[id]
 * Delete a user (admin or super admin only)
 */
export const DELETE = withAuth(async (
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) => {
  try {
    await dbConnect();
    const { id } = await params;

    // Check if user exists
    const user = await Employee.findById(id);
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Prevent deletion of admin and super admin users (safety measure)
    if (user.role === 'admin' || user.role === 'super_admin') {
      return NextResponse.json(
        { error: 'Cannot delete admin or super admin users' },
        { status: 403 }
      );
    }

    // Delete the user
    await Employee.findByIdAndDelete(id);

    return NextResponse.json({
      message: 'User deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting user:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}, { requireAdminOrSuperAdmin: true });

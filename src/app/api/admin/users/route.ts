import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/mongodb';
import Employee from '@/models/Employee';
import { withAuth, getAuthenticatedUser } from '@/lib/middleware/auth';
import { canManageUser, isValidRole } from '@/lib/roles';
import { UserRole } from '@/types/global';

/**
 * GET /api/admin/users
 * Get all users with role information (admin only)
 */
export const GET = withAuth(async (request: NextRequest) => {
  try {
    await dbConnect();

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const role = searchParams.get('role');
    const department = searchParams.get('department');
    const search = searchParams.get('search');

    // Build filter
    const filter: any = {};
    if (role && isValidRole(role)) {
      filter.role = role;
    }
    if (department) {
      filter.department = department;
    }
    if (search) {
      filter.$or = [
        { name: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
      ];
    }

    // Get total count
    const total = await Employee.countDocuments(filter);

    // Get users with pagination
    const users = await Employee.find(filter)
      .select('-password')
      .sort({ createdAt: -1 })
      .skip((page - 1) * limit)
      .limit(limit)
      .lean();

    return NextResponse.json({
      users,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
      message: 'Users retrieved successfully'
    });

  } catch (error) {
    console.error('Error fetching users:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}, { requireAdmin: true });

/**
 * POST /api/admin/users
 * Create a new user (admin only)
 */
export const POST = withAuth(async (request: NextRequest) => {
  try {
    await dbConnect();

    const body = await request.json();
    const { name, email, role, department, position, organization } = body;

    // Validate required fields
    if (!name || !email || !organization) {
      return NextResponse.json(
        { error: 'Name, email, and organization are required' },
        { status: 400 }
      );
    }

    // Validate role
    if (role && !isValidRole(role)) {
      return NextResponse.json(
        { error: 'Invalid role specified' },
        { status: 400 }
      );
    }

    // Check if user already exists within the organization
    const existingUser = await Employee.findOne({
      email,
      organization
    });
    if (existingUser) {
      return NextResponse.json(
        { error: 'User with this email already exists in this organization' },
        { status: 409 }
      );
    }

    // Create new user
    const newUser = new Employee({
      organization,
      name,
      email,
      role: role || 'employee',
      department,
      position,
    });

    await newUser.save();

    // Return user without password
    const userResponse = await Employee.findById(newUser._id).select('-password');

    return NextResponse.json({
      user: userResponse,
      message: 'User created successfully'
    }, { status: 201 });

  } catch (error) {
    console.error('Error creating user:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}, { requireAdmin: true });

import mongoose from 'mongoose';

export interface IOrganization extends mongoose.Document {
  _id: string;
  name: string;
  domain: string;
  subdomain: string;
  logo?: string;
  description?: string;
  industry?: string;
  size?: 'startup' | 'small' | 'medium' | 'large' | 'enterprise';
  website?: string;
  address?: {
    street?: string;
    city?: string;
    state?: string;
    country?: string;
    zipCode?: string;
  };
  settings: {
    allowPublicProfiles: boolean;
    requireEmailVerification: boolean;
    enableAchievements: boolean;
    enableThanks: boolean;
    customBranding: boolean;
    maxEmployees: number;
  };
  subscription: {
    plan: 'free' | 'basic' | 'premium' | 'enterprise';
    status: 'active' | 'inactive' | 'suspended' | 'cancelled';
    startDate: Date;
    endDate?: Date;
    features: string[];
  };
  billing?: {
    email?: string;
    companyName?: string;
    taxId?: string;
    address?: {
      street?: string;
      city?: string;
      state?: string;
      country?: string;
      zipCode?: string;
    };
  };
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const OrganizationSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Organization name is required'],
    trim: true,
    maxlength: [100, 'Organization name cannot be more than 100 characters'],
  },
  domain: {
    type: String,
    required: [true, 'Domain is required'],
    unique: true,
    lowercase: true,
    trim: true,
    match: [
      /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9](?:\.[a-zA-Z]{2,})+$/,
      'Please enter a valid domain',
    ],
  },
  subdomain: {
    type: String,
    required: [true, 'Subdomain is required'],
    unique: true,
    lowercase: true,
    trim: true,
    match: [
      /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9]$/,
      'Please enter a valid subdomain',
    ],
  },
  logo: {
    type: String,
    trim: true,
  },
  description: {
    type: String,
    maxlength: [500, 'Description cannot be more than 500 characters'],
    trim: true,
  },
  industry: {
    type: String,
    trim: true,
    maxlength: [50, 'Industry cannot be more than 50 characters'],
  },
  size: {
    type: String,
    enum: ['startup', 'small', 'medium', 'large', 'enterprise'],
    default: 'small',
  },
  website: {
    type: String,
    trim: true,
  },
  address: {
    street: { type: String, trim: true },
    city: { type: String, trim: true },
    state: { type: String, trim: true },
    country: { type: String, trim: true },
    zipCode: { type: String, trim: true },
  },
  settings: {
    allowPublicProfiles: { type: Boolean, default: true },
    requireEmailVerification: { type: Boolean, default: false },
    enableAchievements: { type: Boolean, default: true },
    enableThanks: { type: Boolean, default: true },
    customBranding: { type: Boolean, default: false },
    maxEmployees: { type: Number, default: 50 },
  },
  subscription: {
    plan: {
      type: String,
      enum: ['free', 'basic', 'premium', 'enterprise'],
      default: 'free',
    },
    status: {
      type: String,
      enum: ['active', 'inactive', 'suspended', 'cancelled'],
      default: 'active',
    },
    startDate: { type: Date, default: Date.now },
    endDate: { type: Date },
    features: [{ type: String }],
  },
  billing: {
    email: { type: String, trim: true },
    companyName: { type: String, trim: true },
    taxId: { type: String, trim: true },
    address: {
      street: { type: String, trim: true },
      city: { type: String, trim: true },
      state: { type: String, trim: true },
      country: { type: String, trim: true },
      zipCode: { type: String, trim: true },
    },
  },
  isActive: {
    type: Boolean,
    default: true,
  },
}, {
  timestamps: true,
});

// Indexes for better performance
// Note: domain and subdomain indexes automatically created by unique: true
OrganizationSchema.index({ 'subscription.status': 1 });
OrganizationSchema.index({ isActive: 1 });

// Compound indexes
OrganizationSchema.index({ domain: 1, isActive: 1 });
OrganizationSchema.index({ subdomain: 1, isActive: 1 });

// Virtual for employee count (can be populated)
OrganizationSchema.virtual('employeeCount', {
  ref: 'Employee',
  localField: '_id',
  foreignField: 'organization',
  count: true,
});

// Ensure virtual fields are serialized
OrganizationSchema.set('toJSON', { virtuals: true });
OrganizationSchema.set('toObject', { virtuals: true });

// Pre-save middleware to ensure subdomain is unique
OrganizationSchema.pre('save', async function(next) {
  if (this.isNew || this.isModified('subdomain')) {
    const existing = await mongoose.models.Organization.findOne({
      subdomain: this.subdomain,
      _id: { $ne: this._id }
    });
    
    if (existing) {
      const error = new Error('Subdomain is already taken');
      return next(error);
    }
  }
  next();
});

export default mongoose.models.Organization || mongoose.model<IOrganization>('Organization', OrganizationSchema);

import mongoose, { Document, Schema } from 'mongoose';
import { UserRole } from '@/types/global';

export interface IEmployee extends Document {
  _id: string;
  organization: mongoose.Types.ObjectId;
  name: string;
  email: string;
  password?: string;
  role: UserRole;
  image?: string;
  bio?: string;
  department?: string;
  position?: string;
  startDate?: Date;
  skills?: string[];
  interests?: string[];
  location?: string;
  linkedIn?: string;
  github?: string;
  isActive: boolean;
  lastLogin?: Date;
  createdAt: Date;
  updatedAt: Date;
}

const EmployeeSchema = new Schema<IEmployee>(
  {
    organization: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Organization',
      required: [true, 'Organization is required'],
    },
    name: {
      type: String,
      required: [true, 'Name is required'],
      trim: true,
      maxlength: [100, 'Name cannot be more than 100 characters'],
    },
    email: {
      type: String,
      required: [true, 'Email is required'],
      lowercase: true,
      trim: true,
      match: [
        /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,
        'Please enter a valid email',
      ],
    },
    password: {
      type: String,
      select: false, // Don't include password in queries by default
    },
    role: {
      type: String,
      enum: ['employee', 'manager', 'hr', 'admin'],
      default: 'employee',
      required: [true, 'Role is required'],
    },
    image: {
      type: String,
      trim: true,
    },
    bio: {
      type: String,
      maxlength: [500, 'Bio cannot be more than 500 characters'],
      trim: true,
    },
    department: {
      type: String,
      trim: true,
      maxlength: [50, 'Department cannot be more than 50 characters'],
    },
    position: {
      type: String,
      trim: true,
      maxlength: [100, 'Position cannot be more than 100 characters'],
    },
    startDate: {
      type: Date,
    },
    skills: [{
      type: String,
      trim: true,
      maxlength: [30, 'Skill cannot be more than 30 characters'],
    }],
    interests: [{
      type: String,
      trim: true,
      maxlength: [30, 'Interest cannot be more than 30 characters'],
    }],
    location: {
      type: String,
      trim: true,
      maxlength: [100, 'Location cannot be more than 100 characters'],
    },
    linkedIn: {
      type: String,
      trim: true,
    },
    github: {
      type: String,
      trim: true,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    lastLogin: {
      type: Date,
    },
  },
  {
    timestamps: true,
  }
);

// Create indexes for better performance and multi-tenancy
// Compound indexes for organization-scoped queries
EmployeeSchema.index({ organization: 1, email: 1 }, { unique: true }); // Unique email per organization
EmployeeSchema.index({ organization: 1, department: 1 });
EmployeeSchema.index({ organization: 1, role: 1 });
EmployeeSchema.index({ organization: 1, isActive: 1 });
EmployeeSchema.index({ organization: 1, name: 'text', bio: 'text' });

// Single field indexes
// Note: organization index covered by compound indexes above
EmployeeSchema.index({ email: 1 }); // For login purposes

export default mongoose.models.Employee || mongoose.model<IEmployee>('Employee', EmployeeSchema);

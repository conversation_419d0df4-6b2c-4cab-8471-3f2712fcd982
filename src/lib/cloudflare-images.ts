/**
 * Cloudflare Images API integration
 * Handles image upload, compression, and management
 */

interface CloudflareImageResponse {
  result: {
    id: string;
    filename: string;
    uploaded: string;
    requireSignedURLs: boolean;
    variants: string[];
  };
  success: boolean;
  errors: any[];
  messages: any[];
}

interface CloudflareConfig {
  accountId: string;
  apiToken: string;
  baseUrl: string;
}

class CloudflareImagesService {
  private config: CloudflareConfig;

  constructor() {
    this.config = {
      accountId: process.env.CLOUDFLARE_ACCOUNT_ID || '',
      apiToken: process.env.CLOUDFLARE_API_TOKEN || '',
      baseUrl: process.env.CLOUDFLARE_IMAGES_BASE_URL || '',
    };

    if (!this.config.accountId || !this.config.apiToken) {
      throw new Error('Cloudflare configuration is missing. Please set CLOUDFLARE_ACCOUNT_ID and CLOUDFLARE_API_TOKEN environment variables.');
    }
  }

  /**
   * Upload image to Cloudflare Images
   */
  async uploadImage(
    imageBuffer: Buffer,
    filename: string,
    metadata?: Record<string, string>
  ): Promise<string> {
    try {
      const formData = new FormData();
      
      // Create a Blob from the buffer
      const blob = new Blob([imageBuffer], { type: this.getMimeType(filename) });
      formData.append('file', blob, filename);

      // Add metadata if provided
      if (metadata) {
        Object.entries(metadata).forEach(([key, value]) => {
          formData.append(`metadata[${key}]`, value);
        });
      }

      const response = await fetch(
        `https://api.cloudflare.com/client/v4/accounts/${this.config.accountId}/images/v1`,
        {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${this.config.apiToken}`,
          },
          body: formData,
        }
      );

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Cloudflare upload failed: ${response.status} - ${errorText}`);
      }

      const result: CloudflareImageResponse = await response.json();

      if (!result.success) {
        throw new Error(`Cloudflare upload failed: ${JSON.stringify(result.errors)}`);
      }

      // Return the image URL with variant for optimization
      return this.getImageUrl(result.result.id, 'profile');
    } catch (error) {
      console.error('Error uploading to Cloudflare:', error);
      throw error;
    }
  }

  /**
   * Delete image from Cloudflare Images
   */
  async deleteImage(imageId: string): Promise<boolean> {
    try {
      const response = await fetch(
        `https://api.cloudflare.com/client/v4/accounts/${this.config.accountId}/images/v1/${imageId}`,
        {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${this.config.apiToken}`,
          },
        }
      );

      return response.ok;
    } catch (error) {
      console.error('Error deleting from Cloudflare:', error);
      return false;
    }
  }

  /**
   * Get optimized image URL with variant
   */
  getImageUrl(imageId: string, variant: string = 'public'): string {
    if (this.config.baseUrl) {
      return `${this.config.baseUrl}/${imageId}/${variant}`;
    }
    return `https://imagedelivery.net/${this.config.accountId}/${imageId}/${variant}`;
  }

  /**
   * Extract image ID from Cloudflare URL
   */
  extractImageId(url: string): string | null {
    const match = url.match(/\/([a-f0-9-]{36})\//);
    return match ? match[1] : null;
  }

  /**
   * Get MIME type from filename
   */
  private getMimeType(filename: string): string {
    const ext = filename.toLowerCase().split('.').pop();
    switch (ext) {
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      case 'png':
        return 'image/png';
      case 'gif':
        return 'image/gif';
      case 'webp':
        return 'image/webp';
      default:
        return 'image/jpeg';
    }
  }
}

export default CloudflareImagesService;
